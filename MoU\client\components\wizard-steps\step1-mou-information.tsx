"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Building, Calendar, Info, FileText } from "lucide-react"
import { Step1Data, MouApplication, mouApplicationService } from "@/lib/services/mou-application.service"

interface MouOption {
  id: string
  mouId: string
  party: {
    id: string
    name: string
    signatory: string
    position: string
  }
}

interface Step1MouInformationProps {
  data: Step1Data
  onChange: (data: Step1Data) => void
  application: MouApplication | null
}

export function Step1MouInformation({ data, onChange, application }: Step1MouInformationProps) {
  const [error, setError] = useState("")
  const [availableMous, setAvailableMous] = useState<MouOption[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAvailableMous()
  }, [])

  const loadAvailableMous = async () => {
    try {
      setLoading(true)
      const mous = await mouApplicationService.getAvailableMous()
      setAvailableMous(mous)
    } catch (err) {
      setError("Failed to load available MoUs")
    } finally {
      setLoading(false)
    }
  }

  const handleDurationChange = (value: string) => {
    const years = parseInt(value)
    if (years >= 1 && years <= 10) {
      onChange({
        ...data,
        mouDurationYears: years
      })
    }
  }

  const handleMouSelection = (mouId: string) => {
    onChange({
      ...data,
      selectedMouId: mouId
    })
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-cyan-900 mb-2">MoU Information</h3>
        <p className="text-muted-foreground">
          Select the Memorandum of Understanding you want to apply for and set the duration.
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {/* MoU Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <FileText className="h-4 w-4 text-cyan-600" />
              Select MoU
            </CardTitle>
            <CardDescription>
              Choose the Memorandum of Understanding to apply for
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="selectedMou">Available MoUs *</Label>
              {loading ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-600"></div>
                  <span className="ml-2 text-sm text-muted-foreground">Loading MoUs...</span>
                </div>
              ) : availableMous.length === 0 ? (
                <div className="text-sm text-muted-foreground py-4 text-center">
                  No MoUs available for your organization
                </div>
              ) : (
                <Select
                  value={data.selectedMouId || ""}
                  onValueChange={handleMouSelection}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a MoU" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableMous.map((mou) => (
                      <SelectItem key={mou.id} value={mou.id}>
                        {mou.mouId} - {mou.party.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              <p className="text-xs text-muted-foreground">
                Select the MoU you want to create an application for
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Organization Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Building className="h-4 w-4 text-cyan-600" />
              Organization Information
            </CardTitle>
            <CardDescription>
              Your organization details (read-only)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="organizationName">Organization Name</Label>
              <Input
                id="organizationName"
                value={data.organizationName}
                readOnly
                className="bg-gray-50"
              />
              <p className="text-xs text-muted-foreground">
                This information is automatically populated from your account
              </p>
            </div>
          </CardContent>
        </Card>

        {/* MoU Duration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Calendar className="h-4 w-4 text-cyan-600" />
              MoU Duration
            </CardTitle>
            <CardDescription>
              Set the duration for this MoU agreement
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="mouDurationYears">Duration (Years) *</Label>
              <Select
                value={data.mouDurationYears.toString()}
                onValueChange={handleDurationChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 10 }, (_, i) => i + 1).map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year} {year === 1 ? 'Year' : 'Years'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Duration must be between 1 and 10 years
              </p>
            </div>
          </CardContent>
        </Card>
      </div>



      {/* Validation Summary */}
      <Card className="bg-cyan-50 border-cyan-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-cyan-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-cyan-900">Step 1 Requirements</h4>
              <ul className="text-sm text-cyan-800 mt-2 space-y-1">
                <li className={`flex items-center gap-2 ${data.selectedMouId ? 'text-green-700' : ''}`}>
                  {data.selectedMouId ? '✓' : '○'} Select a MoU
                </li>
                <li className={`flex items-center gap-2 ${data.mouDurationYears >= 1 && data.mouDurationYears <= 10 ? 'text-green-700' : ''}`}>
                  {data.mouDurationYears >= 1 && data.mouDurationYears <= 10 ? '✓' : '○'} Set MoU duration (1-10 years)
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
