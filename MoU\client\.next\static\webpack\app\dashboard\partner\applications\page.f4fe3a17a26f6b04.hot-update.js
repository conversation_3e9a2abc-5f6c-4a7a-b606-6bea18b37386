"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/page",{

/***/ "(app-pages-browser)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/auth.service */ \"(app-pages-browser)/./lib/services/auth.service.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Import the auth service at the top\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check if user is logged in\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": async ()=>{\n                    try {\n                        const token = localStorage.getItem(\"token\");\n                        if (token) {\n                            // Validate token by getting current user\n                            const userData = await _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.getCurrentUser();\n                            setUser(userData);\n                        }\n                    } catch (error) {\n                        console.error(\"Authentication error:\", error);\n                        // Clear invalid tokens\n                        localStorage.removeItem(\"token\");\n                        localStorage.removeItem(\"refreshToken\");\n                        localStorage.removeItem(\"user\");\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Update the login function to use the actual API\n    const login = async (email, password)=>{\n        setLoading(true);\n        try {\n            const response = await _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Store tokens in localStorage\n            localStorage.setItem(\"token\", response.accessToken);\n            localStorage.setItem(\"refreshToken\", response.refreshToken);\n            localStorage.setItem(\"user\", JSON.stringify(response.user));\n            setUser(response.user);\n        // Do not return anything to match the context type\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Register function for multi-step registration\n    const register = async (data)=>{\n        setLoading(true);\n        try {\n            const response = await _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(data);\n            return response;\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        setLoading(true);\n        try {\n            await _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.logout();\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"refreshToken\");\n            localStorage.removeItem(\"user\");\n            setUser(null);\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            // Clear local storage even if API call fails\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"refreshToken\");\n            localStorage.removeItem(\"user\");\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const verifyAccount = async (token)=>{\n        setLoading(true);\n        try {\n            await _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.verifyEmail(token);\n            // Refresh user data after verification\n            const userData = await _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.getCurrentUser();\n            setUser(userData);\n            localStorage.setItem(\"user\", JSON.stringify(userData));\n        } catch (error) {\n            console.error(\"Verification error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const forgotPassword = async (email)=>{\n        setLoading(true);\n        try {\n            await _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.forgotPassword(email);\n        } catch (error) {\n            console.error(\"Forgot password error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetPassword = async (token, password)=>{\n        setLoading(true);\n        try {\n            await _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.resetPassword(token, password);\n        } catch (error) {\n            console.error(\"Reset password error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getCurrentUser = async ()=>{\n        setLoading(true);\n        try {\n            const userData = await _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.getCurrentUser();\n            setUser(userData);\n            localStorage.setItem(\"user\", JSON.stringify(userData));\n        } catch (error) {\n            console.error(\"Get current user error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            loading,\n            login,\n            register,\n            logout,\n            verifyAccount,\n            forgotPassword,\n            resetPassword,\n            getCurrentUser\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/auth-context.tsx\n"));

/***/ })

});